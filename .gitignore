# Database and State Files
/session_state.json

# Environment and Configuration
/.env
/.env.*
/.env-*
/credentials.enc

# Python Environment
/venv/
/.venv/
__pycache__/
*/__pycache__/
**/__pycache__/
*.pyc
*.pyo
*.pyd
*.so
.Python
*.egg-info/
dist/
build/

# Development Tools
/.vscode/
.DS_Store
Thumbs.db

# Data and Cache Directories
/Cache/
/cache/
/archive/

# Log Files
/Logs/
*.log

# Temporary and Development Files
*.tmp
*.temp
*~
*.bak
*.backup*
*_temp.py
*_debug.py

# Legacy and Deprecated Files
/__init__.py
/backup_before_migration
/.pytest_cache
ai_prompts.bak.*
.codacy/logs/codacy-cli.log
Logs/api_utils.log
Logs/test.log
Logs/api_utils.log
.codacy/logs/codacy-cli.log
Logs/api_utils.log
IMPLEMENTATION_PLAN.md
/ai_prompts.bak.*
ancestry.db-shm
ancestry.db-wal
